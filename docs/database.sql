-- API Service 数据库初始化脚本

-- 创建用户表
CREATE TABLE api_user_t (
    user_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password VARCHAR(255) NOT NULL COMMENT '密码哈希',
    full_name VARCHAR(100) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    role VARCHAR(20) NOT NULL COMMENT '角色',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login TIMESTAMP NULL COMMENT '最后登录时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建用户表索引
CREATE INDEX idx_user_username ON api_user_t(username);
CREATE INDEX idx_user_email ON api_user_t(email);
CREATE INDEX idx_user_role ON api_user_t(role);
CREATE INDEX idx_user_status ON api_user_t(status);
CREATE INDEX idx_user_department ON api_user_t(department);
CREATE INDEX idx_user_create_time ON api_user_t(create_time);

-- 创建系统配置表
CREATE TABLE api_system_config_t (
    config_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type VARCHAR(20) DEFAULT 'string' COMMENT '配置类型',
    description VARCHAR(255) COMMENT '配置描述',
    is_encrypted TINYINT(1) DEFAULT 0 COMMENT '是否加密',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- 创建系统配置表索引
CREATE INDEX idx_config_key ON api_system_config_t(config_key);
CREATE INDEX idx_config_type ON api_system_config_t(config_type);

-- 创建操作日志表
CREATE TABLE api_operation_log_t (
    log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id BIGINT COMMENT '操作用户ID',
    operation VARCHAR(100) NOT NULL COMMENT '操作类型',
    resource VARCHAR(100) COMMENT '操作资源',
    resource_id VARCHAR(100) COMMENT '资源ID',
    request_data TEXT COMMENT '请求数据',
    response_data TEXT COMMENT '响应数据',
    ip_address VARCHAR(45) COMMENT 'IP地址',
    user_agent VARCHAR(500) COMMENT '用户代理',
    status VARCHAR(20) COMMENT '操作状态',
    error_message TEXT COMMENT '错误信息',
    execution_time INT COMMENT '执行时间(毫秒)',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- 创建操作日志表索引
CREATE INDEX idx_log_user_id ON api_operation_log_t(user_id);
CREATE INDEX idx_log_operation ON api_operation_log_t(operation);
CREATE INDEX idx_log_resource ON api_operation_log_t(resource);
CREATE INDEX idx_log_status ON api_operation_log_t(status);
CREATE INDEX idx_log_create_time ON api_operation_log_t(create_time);

-- 创建API访问统计表
CREATE TABLE api_access_stats_t (
    stats_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '统计ID',
    api_path VARCHAR(200) NOT NULL COMMENT 'API路径',
    method VARCHAR(10) NOT NULL COMMENT 'HTTP方法',
    total_requests BIGINT DEFAULT 0 COMMENT '总请求数',
    success_requests BIGINT DEFAULT 0 COMMENT '成功请求数',
    failed_requests BIGINT DEFAULT 0 COMMENT '失败请求数',
    avg_response_time DECIMAL(10,2) DEFAULT 0 COMMENT '平均响应时间(毫秒)',
    max_response_time INT DEFAULT 0 COMMENT '最大响应时间(毫秒)',
    min_response_time INT DEFAULT 0 COMMENT '最小响应时间(毫秒)',
    last_access_time TIMESTAMP NULL COMMENT '最后访问时间',
    stats_date DATE NOT NULL COMMENT '统计日期',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API访问统计表';

-- 创建API访问统计表索引
CREATE UNIQUE INDEX idx_stats_path_date ON api_access_stats_t(api_path, method, stats_date);
CREATE INDEX idx_stats_date ON api_access_stats_t(stats_date);
CREATE INDEX idx_stats_path ON api_access_stats_t(api_path);

-- 创建数据字典表
CREATE TABLE api_data_dict_t (
    dict_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '字典ID',
    dict_type VARCHAR(50) NOT NULL COMMENT '字典类型',
    dict_key VARCHAR(100) NOT NULL COMMENT '字典键',
    dict_value VARCHAR(200) NOT NULL COMMENT '字典值',
    dict_label VARCHAR(200) NOT NULL COMMENT '字典标签',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    remark VARCHAR(500) COMMENT '备注',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据字典表';

-- 创建数据字典表索引
CREATE UNIQUE INDEX idx_dict_type_key ON api_data_dict_t(dict_type, dict_key);
CREATE INDEX idx_dict_type ON api_data_dict_t(dict_type);
CREATE INDEX idx_dict_status ON api_data_dict_t(status);

-- 插入初始数据

-- 插入默认管理员用户
INSERT INTO api_user_t (username, email, password, full_name, role, status) VALUES 
('admin', '<EMAIL>', SHA2('admin123', 256), '系统管理员', 'admin', 'active');

-- 插入系统配置
INSERT INTO api_system_config_t (config_key, config_value, config_type, description) VALUES 
('system.name', 'API Service', 'string', '系统名称'),
('system.version', '1.0.0', 'string', '系统版本'),
('cache.default_expire', '3600', 'number', '默认缓存过期时间(秒)'),
('api.rate_limit', '100', 'number', 'API请求频率限制(每秒)'),
('log.retention_days', '30', 'number', '日志保留天数'),
('security.password_min_length', '6', 'number', '密码最小长度'),
('security.session_timeout', '1800', 'number', '会话超时时间(秒)');

-- 插入数据字典
INSERT INTO api_data_dict_t (dict_type, dict_key, dict_value, dict_label, sort_order) VALUES 
('user_role', 'admin', 'admin', '管理员', 1),
('user_role', 'user', 'user', '普通用户', 2),
('user_role', 'guest', 'guest', '访客', 3),
('user_status', 'active', 'active', '激活', 1),
('user_status', 'inactive', 'inactive', '禁用', 2),
('user_status', 'locked', 'locked', '锁定', 3),
('operation_type', 'create', 'create', '创建', 1),
('operation_type', 'read', 'read', '查询', 2),
('operation_type', 'update', 'update', '更新', 3),
('operation_type', 'delete', 'delete', '删除', 4),
('config_type', 'string', 'string', '字符串', 1),
('config_type', 'number', 'number', '数字', 2),
('config_type', 'boolean', 'boolean', '布尔值', 3),
('config_type', 'json', 'json', 'JSON对象', 4);
