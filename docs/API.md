# API Service 接口文档

## 概述

API Service 是基于ES2+框架开发的通用API服务，提供用户管理、数据管理、系统监控等功能。

## 基础信息

- **服务地址**: http://localhost:9004
- **API前缀**: /api
- **请求方式**: POST (除健康检查接口外)
- **数据格式**: JSON

## 通用请求格式

所有POST请求都需要包含header信息：

```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "UNIQUE_REQUEST_ID"
  },
  // 其他请求参数...
}
```

## 通用响应格式

```json
{
  "header": {
    "functionExecutionStatus": {
      "status": "EXECUTED-SUCCESS", // 或 "EXECUTED-FAIL"
      "statusCodeData": {
        "subjectCode": "...",
        "reasonCode": "...",
        "message": "..."
      }
    }
  },
  // 其他响应数据...
}
```

## 用户管理接口

### 1. 创建用户

**接口地址**: `POST /api/user/create`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CREATE_USER_001"
  },
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Test User",
  "phone": "13800138000",
  "department": "IT",
  "role": "user"
}
```

**响应示例**:
```json
{
  "header": {
    "functionExecutionStatus": {
      "status": "EXECUTED-SUCCESS"
    }
  },
  "userId": 1,
  "username": "testuser",
  "message": "用户创建成功"
}
```

### 2. 获取用户信息

**接口地址**: `POST /api/user/get`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "GET_USER_001"
  },
  "userId": 1
}
```

**响应示例**:
```json
{
  "header": {
    "functionExecutionStatus": {
      "status": "EXECUTED-SUCCESS"
    }
  },
  "user": {
    "userId": 1,
    "username": "testuser",
    "email": "<EMAIL>",
    "fullName": "Test User",
    "phone": "13800138000",
    "department": "IT",
    "role": "user",
    "status": "active",
    "createTime": "2025-08-07T10:00:00Z",
    "updateTime": "2025-08-07T10:00:00Z"
  }
}
```

### 3. 用户列表

**接口地址**: `POST /api/user/list`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "LIST_USERS_001"
  },
  "pageNum": 1,
  "pageSize": 20,
  "username": "",
  "email": "",
  "department": "",
  "role": "user",
  "status": "active"
}
```

**响应示例**:
```json
{
  "header": {
    "functionExecutionStatus": {
      "status": "EXECUTED-SUCCESS"
    }
  },
  "users": [
    {
      "userId": 1,
      "username": "testuser",
      "email": "<EMAIL>",
      "fullName": "Test User",
      "role": "user",
      "status": "active"
    }
  ],
  "total": 1,
  "pageNum": 1,
  "pageSize": 20,
  "totalPages": 1
}
```

### 4. 更新用户

**接口地址**: `POST /api/user/update`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "UPDATE_USER_001"
  },
  "userId": 1,
  "email": "<EMAIL>",
  "fullName": "New Full Name",
  "phone": "13900139000",
  "department": "HR",
  "role": "admin",
  "status": "active"
}
```

### 5. 删除用户

**接口地址**: `POST /api/user/delete`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "DELETE_USER_001"
  },
  "userId": 1
}
```

## 数据管理接口

### 1. 查询数据

**接口地址**: `POST /api/data/query`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "QUERY_DATA_001"
  },
  "tableName": "user_data",
  "conditions": {
    "status": "active",
    "department": "IT"
  },
  "orderBy": "create_time DESC",
  "pageNum": 1,
  "pageSize": 20
}
```

### 2. 创建数据

**接口地址**: `POST /api/data/create`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CREATE_DATA_001"
  },
  "tableName": "user_data",
  "data": {
    "name": "Test Data",
    "value": "123",
    "status": "active"
  }
}
```

### 3. 更新数据

**接口地址**: `POST /api/data/update`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "UPDATE_DATA_001"
  },
  "tableName": "user_data",
  "id": 1,
  "data": {
    "name": "Updated Data",
    "value": "456",
    "status": "inactive"
  }
}
```

### 4. 删除数据

**接口地址**: `POST /api/data/delete`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "DELETE_DATA_001"
  },
  "tableName": "user_data",
  "id": 1
}
```

### 5. 批量操作

**接口地址**: `POST /api/data/batch`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "BATCH_DATA_001"
  },
  "tableName": "user_data",
  "operation": "create",
  "items": [
    {
      "name": "Data 1",
      "value": "111"
    },
    {
      "name": "Data 2",
      "value": "222"
    }
  ]
}
```

### 6. 数据统计

**接口地址**: `POST /api/data/statistics`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "STATS_DATA_001"
  },
  "tableName": "user_data",
  "groupBy": ["department"],
  "aggregates": {
    "id": "count",
    "value": "sum"
  },
  "conditions": {
    "status": "active"
  },
  "dateRange": {
    "startDate": "2025-01-01",
    "endDate": "2025-12-31"
  }
}
```

## 系统管理接口

### 1. 系统信息

**接口地址**: `POST /api/system/info`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "SYSTEM_INFO_001"
  }
}
```

### 2. 系统配置

**接口地址**: `POST /api/system/config`

### 3. 缓存管理

**接口地址**: `POST /api/system/cache`

**请求参数**:
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CACHE_MANAGE_001"
  },
  "action": "clear",
  "pattern": "users:*"
}
```

支持的操作：
- `clear`: 清空缓存
- `stats`: 获取缓存统计
- `keys`: 获取所有缓存键
- `get`: 获取指定缓存
- `delete`: 删除指定缓存

### 4. 系统统计

**接口地址**: `POST /api/system/stats`

## 健康检查接口

### 1. 健康检查

**接口地址**: `GET /api/health/check`

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-08-07T10:00:00Z",
  "checks": {
    "database": {
      "status": "healthy",
      "duration": 10
    },
    "cache": {
      "status": "healthy",
      "duration": 5
    }
  },
  "uptime": 3600,
  "version": "1.0.0"
}
```

### 2. 简单Ping

**接口地址**: `GET /api/health/ping`

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2025-08-07T10:00:00Z",
  "message": "pong"
}
```

### 3. 服务状态

**接口地址**: `GET /api/health/status`

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| EXECUTED-SUCCESS | 执行成功 |
| EXECUTED-FAIL | 执行失败 |
| VALIDATION_ERROR | 参数验证错误 |
| NOT_FOUND | 资源不存在 |
| ALREADY_EXISTS | 资源已存在 |
| INTERNAL_ERROR | 内部错误 |
| DATABASE_ERROR | 数据库错误 |
| CACHE_ERROR | 缓存错误 |

## 使用示例

### cURL示例

```bash
# 创建用户
curl -X POST http://localhost:9004/api/user/create \
  -H "Content-Type: application/json" \
  -d '{
    "header": {
      "functionRequesterIdentifier": "API_CLIENT",
      "functionCallIdentifier": "CREATE_USER_001"
    },
    "username": "testuser",
    "email": "<EMAIL>",
    "password": "password123",
    "fullName": "Test User",
    "role": "user"
  }'

# 健康检查
curl -X GET http://localhost:9004/api/health/check
```

### JavaScript示例

```javascript
// 创建用户
const createUser = async () => {
  const response = await fetch('http://localhost:9004/api/user/create', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      header: {
        functionRequesterIdentifier: 'API_CLIENT',
        functionCallIdentifier: 'CREATE_USER_001'
      },
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
      fullName: 'Test User',
      role: 'user'
    })
  });
  
  const result = await response.json();
  console.log(result);
};
```
