package tests

import (
	"api-service/src/bo/request"
	"api-service/src/bo/response"
	"encoding/json"
	"testing"
)

func TestUserRequestSerialization(t *testing.T) {
	// 测试用户创建请求序列化
	createReq := &request.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
		FullName: "Test User",
		Phone:    "13800138000",
		Role:     "user",
	}
	
	// 序列化
	jsonData, err := json.Marshal(createReq)
	if err != nil {
		t.Fatalf("Failed to marshal CreateUserRequest: %v", err)
	}
	
	// 反序列化
	var unmarshaled request.CreateUserRequest
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal CreateUserRequest: %v", err)
	}
	
	// 验证字段
	if unmarshaled.Username != "testuser" {
		t.<PERSON>("Expected username 'testuser', got '%s'", unmarshaled.Username)
	}
	if unmarshaled.Email != "<EMAIL>" {
		t.<PERSON><PERSON><PERSON>("Expected email '<EMAIL>', got '%s'", unmarshaled.Email)
	}
}

func TestUserResponseSerialization(t *testing.T) {
	// 测试用户信息响应序列化
	userInfo := &response.UserInfo{
		UserId:   1,
		Username: "testuser",
		Email:    "<EMAIL>",
		FullName: "Test User",
		Role:     "user",
		Status:   "active",
	}
	
	// 序列化
	jsonData, err := json.Marshal(userInfo)
	if err != nil {
		t.Fatalf("Failed to marshal UserInfo: %v", err)
	}
	
	// 反序列化
	var unmarshaled response.UserInfo
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal UserInfo: %v", err)
	}
	
	// 验证字段
	if unmarshaled.UserId != 1 {
		t.Errorf("Expected userId 1, got %d", unmarshaled.UserId)
	}
	if unmarshaled.Username != "testuser" {
		t.Errorf("Expected username 'testuser', got '%s'", unmarshaled.Username)
	}
}

func TestListUsersRequest(t *testing.T) {
	// 测试用户列表请求
	listReq := &request.ListUsersRequest{
		PageNum:  1,
		PageSize: 20,
		Role:     "user",
		Status:   "active",
	}
	
	// 序列化
	jsonData, err := json.Marshal(listReq)
	if err != nil {
		t.Fatalf("Failed to marshal ListUsersRequest: %v", err)
	}
	
	// 反序列化
	var unmarshaled request.ListUsersRequest
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal ListUsersRequest: %v", err)
	}
	
	// 验证字段
	if unmarshaled.PageNum != 1 {
		t.Errorf("Expected pageNum 1, got %d", unmarshaled.PageNum)
	}
	if unmarshaled.PageSize != 20 {
		t.Errorf("Expected pageSize 20, got %d", unmarshaled.PageSize)
	}
	if unmarshaled.Role != "user" {
		t.Errorf("Expected role 'user', got '%s'", unmarshaled.Role)
	}
}
