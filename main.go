package main

import (
	"api-service/src"
	"api-service/src/bl/manager"
	"api-service/src/ui"
	"esim.common/src/bl"
	"k/app"
)

func main() {
	param := &app.Param{
		Name:    "API Service",
		Context: "api",
		Feature: app.FeatureDb | app.FeatureClient | app.FeatureVersion | app.FeatureLogEvent,
	}

	// 设置应用参数
	apiParam := &src.ApiParam{}
	param.SetClientParam(apiParam)
	param.VersionInfo = &src.ApiVersion{}

	// 启动应用
	app.StartAppWithCallback(param, nil, func(a *app.App) {
		// 初始化管理器
		app.GetManager[*manager.UserManager](a)
		app.GetManager[*manager.DataManager](a)
		app.GetManager[*manager.CacheManager](a)

		// 如果需要KMS服务，可以启用
		if apiParam.EnableKms {
			km := app.GetManager[*bl.KmsManager](a)
			km.Setup(apiParam.KmsApiKey, apiParam.KmsApiSecret, apiParam.KmsApiUrl, apiParam.KmsApiBackupUrl)
		}

		// 注册API事件处理器
		a.RegisterEvent("/api/user", &ui.UserEvent{})
		a.RegisterEvent("/api/data", &ui.DataEvent{})
		a.RegisterEvent("/api/system", &ui.SystemEvent{})
	})
}
