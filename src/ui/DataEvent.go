package ui

import (
	"api-service/src/bl/handler"
	"k/app"
)

type DataEvent struct {
}

// Query 查询数据
func (de *DataEvent) Query(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.QueryData(request, response)
}

// Create 创建数据
func (de *DataEvent) Create(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.CreateData(request, response)
}

// Update 更新数据
func (de *DataEvent) Update(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.UpdateData(request, response)
}

// Delete 删除数据
func (de *DataEvent) Delete(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.DeleteData(request, response)
}

// Batch 批量操作
func (de *DataEvent) Batch(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.BatchOperation(request, response)
}

// Statistics 数据统计
func (de *DataEvent) Statistics(_ app.IsInstanceEvent, dh *handler.DataHandler, request *app.Request, response *app.Response) {
	dh.GetStatistics(request, response)
}
