package ui

import (
	"api-service/src/bl/handler"
	"k/app"
)

type UserEvent struct {
}

// Create 创建用户
func (ue *UserEvent) Create(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.CreateUser(request, response)
}

// Get 获取用户信息
func (ue *UserEvent) Get(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.GetUser(request, response)
}

// Info 获取用户信息（别名）
func (ue *UserEvent) Info(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.GetUser(request, response)
}

// List 获取用户列表
func (ue *UserEvent) List(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.ListUsers(request, response)
}

// Update 更新用户
func (ue *UserEvent) Update(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.UpdateUser(request, response)
}

// Delete 删除用户
func (ue *UserEvent) Delete(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.DeleteUser(request, response)
}

// ChangePassword 修改密码
func (ue *UserEvent) ChangePassword(_ app.IsInstanceEvent, uh *handler.UserHandler, request *app.Request, response *app.Response) {
	uh.ChangePassword(request, response)
}
