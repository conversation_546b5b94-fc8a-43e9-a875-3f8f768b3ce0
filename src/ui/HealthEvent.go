package ui

import (
	"api-service/src/bl/handler"
	"k/app"
)

type HealthEvent struct {
}

// Check 健康检查
func (he *HealthEvent) Check(_ app.IsInstanceEvent, hh *handler.HealthHandler, request *app.Request, response *app.Response) {
	hh.HealthCheck(request, response)
}

// Ping 简单ping检查
func (he *HealthEvent) Ping(_ app.IsInstanceEvent, hh *handler.HealthHandler, request *app.Request, response *app.Response) {
	hh.<PERSON>(request, response)
}

// Status 服务状态
func (he *HealthEvent) Status(_ app.IsInstanceEvent, hh *handler.HealthHandler, request *app.Request, response *app.Response) {
	hh.GetServiceStatus(request, response)
}
