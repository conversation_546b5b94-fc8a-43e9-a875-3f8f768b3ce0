package ui

import (
	"api-service/src/bl/handler"
	"k/app"
)

type SystemEvent struct {
}

// Info 获取系统信息
func (se *SystemEvent) Info(_ app.IsInstanceEvent, sh *handler.SystemHandler, request *app.Request, response *app.Response) {
	sh.GetSystemInfo(request, response)
}

// Config 获取系统配置
func (se *SystemEvent) Config(_ app.IsInstanceEvent, sh *handler.SystemHandler, request *app.Request, response *app.Response) {
	sh.GetSystemConfig(request, response)
}

// UpdateConfig 更新系统配置
func (se *SystemEvent) UpdateConfig(_ app.IsInstanceEvent, sh *handler.SystemHandler, request *app.Request, response *app.Response) {
	sh.UpdateSystemConfig(request, response)
}

// Cache 缓存管理
func (se *SystemEvent) Cache(_ app.IsInstanceEvent, sh *handler.SystemHandler, request *app.Request, response *app.Response) {
	sh.<PERSON>age<PERSON><PERSON>(request, response)
}

// Stats 系统统计
func (se *SystemEvent) Stats(_ app.IsInstanceEvent, sh *handler.SystemHandler, request *app.Request, response *app.Response) {
	sh.GetSystemStats(request, response)
}
