package request

import "esim.common/src/bo"

// 数据查询请求
type QueryDataRequest struct {
	bo.RspRequest
	TableName  string                 `json:"tableName" validate:"required"`
	Conditions map[string]interface{} `json:"conditions,omitempty"`
	OrderBy    string                 `json:"orderBy,omitempty"`
	PageNum    int32                  `json:"pageNum" validate:"min=1"`
	PageSize   int32                  `json:"pageSize" validate:"min=1,max=1000"`
}

// 数据创建请求
type CreateDataRequest struct {
	bo.RspRequest
	TableName string                 `json:"tableName" validate:"required"`
	Data      map[string]interface{} `json:"data" validate:"required"`
}

// 数据更新请求
type UpdateDataRequest struct {
	bo.RspRequest
	TableName  string                 `json:"tableName" validate:"required"`
	Id         int64                  `json:"id" validate:"required,gt=0"`
	Data       map[string]interface{} `json:"data" validate:"required"`
}

// 数据删除请求
type DeleteDataRequest struct {
	bo.RspRequest
	TableName string `json:"tableName" validate:"required"`
	Id        int64  `json:"id" validate:"required,gt=0"`
}

// 批量数据操作请求
type BatchDataRequest struct {
	bo.RspRequest
	TableName string                   `json:"tableName" validate:"required"`
	Operation string                   `json:"operation" validate:"required,oneof=create update delete"`
	Items     []map[string]interface{} `json:"items" validate:"required,min=1,max=1000"`
}

// 数据统计请求
type DataStatisticsRequest struct {
	bo.RspRequest
	TableName  string                 `json:"tableName" validate:"required"`
	GroupBy    []string               `json:"groupBy,omitempty"`
	Aggregates map[string]string      `json:"aggregates,omitempty"` // field -> function (count, sum, avg, max, min)
	Conditions map[string]interface{} `json:"conditions,omitempty"`
	DateRange  *DateRange             `json:"dateRange,omitempty"`
}

// 日期范围
type DateRange struct {
	StartDate string `json:"startDate" validate:"required"`
	EndDate   string `json:"endDate" validate:"required"`
}
