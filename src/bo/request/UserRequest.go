package request

import "esim.common/src/bo"

// 用户创建请求
type CreateUserRequest struct {
	bo.RspRequest
	Username    string `json:"username" validate:"required,min=3,max=50"`
	Email       string `json:"email" validate:"required,email"`
	Password    string `json:"password" validate:"required,min=6"`
	FullName    string `json:"fullName" validate:"required,max=100"`
	Phone       string `json:"phone,omitempty" validate:"omitempty,phone"`
	Department  string `json:"department,omitempty" validate:"max=100"`
	Role        string `json:"role" validate:"required,oneof=admin user guest"`
}

// 用户更新请求
type UpdateUserRequest struct {
	bo.RspRequest
	UserId     int64  `json:"userId" validate:"required,gt=0"`
	Email      string `json:"email,omitempty" validate:"omitempty,email"`
	FullName   string `json:"fullName,omitempty" validate:"max=100"`
	Phone      string `json:"phone,omitempty" validate:"omitempty,phone"`
	Department string `json:"department,omitempty" validate:"max=100"`
	Role       string `json:"role,omitempty" validate:"omitempty,oneof=admin user guest"`
	Status     string `json:"status,omitempty" validate:"omitempty,oneof=active inactive"`
}

// 用户查询请求
type GetUserRequest struct {
	bo.RspRequest
	UserId int64 `json:"userId" validate:"required,gt=0"`
}

// 用户列表请求
type ListUsersRequest struct {
	bo.RspRequest
	PageNum    int32  `json:"pageNum" validate:"min=1"`
	PageSize   int32  `json:"pageSize" validate:"min=1,max=1000"`
	Username   string `json:"username,omitempty"`
	Email      string `json:"email,omitempty"`
	Department string `json:"department,omitempty"`
	Role       string `json:"role,omitempty" validate:"omitempty,oneof=admin user guest"`
	Status     string `json:"status,omitempty" validate:"omitempty,oneof=active inactive"`
}

// 用户删除请求
type DeleteUserRequest struct {
	bo.RspRequest
	UserId int64 `json:"userId" validate:"required,gt=0"`
}

// 用户密码修改请求
type ChangePasswordRequest struct {
	bo.RspRequest
	UserId      int64  `json:"userId" validate:"required,gt=0"`
	OldPassword string `json:"oldPassword" validate:"required"`
	NewPassword string `json:"newPassword" validate:"required,min=6"`
}
