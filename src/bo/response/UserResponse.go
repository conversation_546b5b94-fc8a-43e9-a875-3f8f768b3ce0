package response

import (
	"esim.common/src/bo"
	"time"
)

// 用户信息响应
type UserInfo struct {
	UserId     int64     `json:"userId"`
	Username   string    `json:"username"`
	Email      string    `json:"email"`
	FullName   string    `json:"fullName"`
	Phone      string    `json:"phone,omitempty"`
	Department string    `json:"department,omitempty"`
	Role       string    `json:"role"`
	Status     string    `json:"status"`
	CreateTime time.Time `json:"createTime"`
	UpdateTime time.Time `json:"updateTime"`
	LastLogin  time.Time `json:"lastLogin,omitempty"`
}

// 用户创建响应
type CreateUserResponse struct {
	bo.RspResponse
	UserId   int64  `json:"userId"`
	Username string `json:"username"`
	Message  string `json:"message"`
}

func NewCreateUserResponse() *CreateUserResponse {
	return &CreateUserResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 用户详情响应
type GetUserResponse struct {
	bo.RspResponse
	User *UserInfo `json:"user"`
}

func NewGetUserResponse() *GetUserResponse {
	return &GetUserResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 用户列表响应
type ListUsersResponse struct {
	bo.RspResponse
	Users      []*UserInfo `json:"users"`
	Total      int64       `json:"total"`
	PageNum    int32       `json:"pageNum"`
	PageSize   int32       `json:"pageSize"`
	TotalPages int32       `json:"totalPages"`
}

func NewListUsersResponse() *ListUsersResponse {
	return &ListUsersResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 通用操作响应
type OperationResponse struct {
	bo.RspResponse
	Success bool   `json:"success"`
	Message string `json:"message"`
}

func NewOperationResponse() *OperationResponse {
	return &OperationResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}
