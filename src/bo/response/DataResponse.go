package response

import "esim.common/src/bo"

// 数据查询响应
type QueryDataResponse struct {
	bo.RspResponse
	Data       []map[string]interface{} `json:"data"`
	Total      int64                    `json:"total"`
	PageNum    int32                    `json:"pageNum"`
	PageSize   int32                    `json:"pageSize"`
	TotalPages int32                    `json:"totalPages"`
}

func NewQueryDataResponse() *QueryDataResponse {
	return &QueryDataResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 数据创建响应
type CreateDataResponse struct {
	bo.RspResponse
	Id      int64  `json:"id"`
	Success bool   `json:"success"`
	Message string `json:"message"`
}

func NewCreateDataResponse() *CreateDataResponse {
	return &CreateDataResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 数据统计响应
type DataStatisticsResponse struct {
	bo.RspResponse
	Statistics []map[string]interface{} `json:"statistics"`
	Count      int                      `json:"count"`
	Message    string                   `json:"message"`
}

func NewDataStatisticsResponse() *DataStatisticsResponse {
	return &DataStatisticsResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}

// 批量操作响应
type BatchDataResponse struct {
	bo.RspResponse
	Results []int64 `json:"results"`
	Count   int     `json:"count"`
	Success bool    `json:"success"`
	Message string  `json:"message"`
}

func NewBatchDataResponse() *BatchDataResponse {
	return &BatchDataResponse{
		RspResponse: bo.RspResponse{
			Header: &bo.RspResponseHeader{
				FunctionExecutionStatus: bo.NewFunctionExecutionStatusWithSuccess(),
			},
		},
	}
}
