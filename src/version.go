package src

import (
	"k/app"
)

type ApiVersion struct {
}

func (v ApiVersion) Version() *app.Version {
	return &app.Version{
		Major: 1,
		Minor: 0,
		Build: 0,
		Code:  "API-SERVICE",
		Date:  "20250807",
	}
}

func (v ApiVersion) Update(from string) *app.VersionUpdate {
	switch from {
	case "":
		return &app.VersionUpdate{
			To:          "1.0.0",
			Date:        "20250807",
			Description: []string{"API Service初始版本", "支持用户管理", "支持数据管理", "支持系统监控"},
		}
	}
	return nil
}
