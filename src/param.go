package src

type ApiParam struct {
	// KMS相关配置
	EnableKms        bool   `description:"是否启用KMS服务" default:"false" readonly:"false"`
	KmsApiKey        string `description:"KMS API Key" readonly:"true"`
	KmsApiSecret     string `description:"KMS API Secret" encryptBase64:"true" readonly:"true"`
	KmsApiUrl        string `description:"KMS API Url" readonly:"true"`
	KmsApiBackupUrl  string `description:"KMS API Backup Url" readonly:"true"`
	
	// 缓存配置
	CacheExpireTime  int64  `description:"缓存过期时间(秒)" default:"3600" readonly:"false"`
	
	// 性能配置
	MaxConcurrentRequests int64 `description:"最大并发请求数" default:"1000" readonly:"false"`
	RateLimitPerSecond   int64 `description:"每秒请求限制" default:"100" readonly:"false"`
	
	// 业务配置
	EnableDataValidation bool   `description:"是否启用数据验证" default:"true" readonly:"false"`
	DefaultPageSize      int32  `description:"默认分页大小" default:"20" readonly:"false"`
	MaxPageSize          int32  `description:"最大分页大小" default:"1000" readonly:"false"`
	
	// 外部服务配置
	ExternalApiUrl       string `description:"外部API服务地址" readonly:"false"`
	ExternalApiTimeout   int64  `description:"外部API超时时间(毫秒)" default:"30000" readonly:"false"`
	
	// 日志配置
	LogLevel             string `description:"日志级别" default:"INFO" readonly:"false"`
	EnableAccessLog      bool   `description:"是否启用访问日志" default:"true" readonly:"false"`
}

func (ap *ApiParam) ClientParamDescription() string {
	return "API Service Parameters"
}
