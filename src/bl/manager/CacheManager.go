package manager

import (
	"api-service/src"
	"encoding/json"
	"fmt"
	"k/app"
	"k/logger"
	"sync"
	"time"
)

type CacheManager struct {
	A     *app.App
	cache map[string]*CacheItem
	mutex sync.RWMutex
	param *src.ApiParam
}

type CacheItem struct {
	Value      interface{}
	ExpireTime time.Time
}

func (cm *CacheManager) Init() {
	cm.cache = make(map[string]*CacheItem)
	cm.param = app.GetClientParam[*src.ApiParam](cm.A)
	
	// 启动清理过期缓存的goroutine
	go cm.cleanupExpiredItems()
}

// Set 设置缓存
func (cm *CacheManager) Set(key string, value interface{}, expireSeconds int64) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if expireSeconds <= 0 {
		expireSeconds = cm.param.CacheExpireTime
	}
	
	cm.cache[key] = &CacheItem{
		Value:      value,
		ExpireTime: time.Now().Add(time.Duration(expireSeconds) * time.Second),
	}
	
	logger.D("CacheManager", "Set cache", key)
	return nil
}

// Get 获取缓存
func (cm *CacheManager) Get(key string) (interface{}, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	item, exists := cm.cache[key]
	if !exists {
		return nil, false
	}
	
	// 检查是否过期
	if time.Now().After(item.ExpireTime) {
		delete(cm.cache, key)
		return nil, false
	}
	
	logger.D("CacheManager", "Get cache hit", key)
	return item.Value, true
}

// GetString 获取字符串缓存
func (cm *CacheManager) GetString(key string) (string, bool) {
	value, exists := cm.Get(key)
	if !exists {
		return "", false
	}
	
	if str, ok := value.(string); ok {
		return str, true
	}
	
	return "", false
}

// GetInt 获取整数缓存
func (cm *CacheManager) GetInt(key string) (int64, bool) {
	value, exists := cm.Get(key)
	if !exists {
		return 0, false
	}
	
	switch v := value.(type) {
	case int64:
		return v, true
	case int:
		return int64(v), true
	case int32:
		return int64(v), true
	default:
		return 0, false
	}
}

// GetJSON 获取JSON对象缓存
func (cm *CacheManager) GetJSON(key string, target interface{}) bool {
	value, exists := cm.Get(key)
	if !exists {
		return false
	}
	
	if jsonStr, ok := value.(string); ok {
		err := json.Unmarshal([]byte(jsonStr), target)
		return err == nil
	}
	
	return false
}

// SetJSON 设置JSON对象缓存
func (cm *CacheManager) SetJSON(key string, value interface{}, expireSeconds int64) error {
	jsonBytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	
	return cm.Set(key, string(jsonBytes), expireSeconds)
}

// Delete 删除缓存
func (cm *CacheManager) Delete(key string) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	delete(cm.cache, key)
	logger.D("CacheManager", "Delete cache", key)
}

// DeletePattern 删除匹配模式的缓存
func (cm *CacheManager) DeletePattern(pattern string) int {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	count := 0
	for key := range cm.cache {
		if cm.matchPattern(key, pattern) {
			delete(cm.cache, key)
			count++
		}
	}
	
	logger.D("CacheManager", "Delete pattern", pattern, "count", count)
	return count
}

// Exists 检查缓存是否存在
func (cm *CacheManager) Exists(key string) bool {
	_, exists := cm.Get(key)
	return exists
}

// Clear 清空所有缓存
func (cm *CacheManager) Clear() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	cm.cache = make(map[string]*CacheItem)
	logger.D("CacheManager", "Clear all cache")
}

// Size 获取缓存大小
func (cm *CacheManager) Size() int {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	return len(cm.cache)
}

// Keys 获取所有缓存键
func (cm *CacheManager) Keys() []string {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	keys := make([]string, 0, len(cm.cache))
	for key := range cm.cache {
		keys = append(keys, key)
	}
	
	return keys
}

// GetStats 获取缓存统计信息
func (cm *CacheManager) GetStats() map[string]interface{} {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	stats := map[string]interface{}{
		"total_keys":    len(cm.cache),
		"expired_keys":  0,
		"memory_usage":  0,
	}
	
	now := time.Now()
	expiredCount := 0
	
	for _, item := range cm.cache {
		if now.After(item.ExpireTime) {
			expiredCount++
		}
	}
	
	stats["expired_keys"] = expiredCount
	return stats
}

// Increment 递增计数器
func (cm *CacheManager) Increment(key string, delta int64) (int64, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	item, exists := cm.cache[key]
	if !exists {
		// 如果不存在，创建新的计数器
		cm.cache[key] = &CacheItem{
			Value:      delta,
			ExpireTime: time.Now().Add(time.Duration(cm.param.CacheExpireTime) * time.Second),
		}
		return delta, nil
	}
	
	// 检查是否过期
	if time.Now().After(item.ExpireTime) {
		cm.cache[key] = &CacheItem{
			Value:      delta,
			ExpireTime: time.Now().Add(time.Duration(cm.param.CacheExpireTime) * time.Second),
		}
		return delta, nil
	}
	
	// 递增现有值
	if currentValue, ok := item.Value.(int64); ok {
		newValue := currentValue + delta
		item.Value = newValue
		return newValue, nil
	}
	
	return 0, fmt.Errorf("缓存值不是数字类型")
}

// SetExpire 设置缓存过期时间
func (cm *CacheManager) SetExpire(key string, expireSeconds int64) bool {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	item, exists := cm.cache[key]
	if !exists {
		return false
	}
	
	item.ExpireTime = time.Now().Add(time.Duration(expireSeconds) * time.Second)
	return true
}

// cleanupExpiredItems 清理过期缓存项
func (cm *CacheManager) cleanupExpiredItems() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟清理一次
	defer ticker.Stop()
	
	for {
		select {
		case <-ticker.C:
			cm.mutex.Lock()
			now := time.Now()
			expiredKeys := []string{}
			
			for key, item := range cm.cache {
				if now.After(item.ExpireTime) {
					expiredKeys = append(expiredKeys, key)
				}
			}
			
			for _, key := range expiredKeys {
				delete(cm.cache, key)
			}
			
			if len(expiredKeys) > 0 {
				logger.D("CacheManager", "Cleanup expired items", len(expiredKeys))
			}
			
			cm.mutex.Unlock()
		}
	}
}

// matchPattern 简单的模式匹配（支持*通配符）
func (cm *CacheManager) matchPattern(str, pattern string) bool {
	if pattern == "*" {
		return true
	}
	
	// 简单实现，只支持后缀匹配
	if len(pattern) > 0 && pattern[len(pattern)-1] == '*' {
		prefix := pattern[:len(pattern)-1]
		return len(str) >= len(prefix) && str[:len(prefix)] == prefix
	}
	
	return str == pattern
}
