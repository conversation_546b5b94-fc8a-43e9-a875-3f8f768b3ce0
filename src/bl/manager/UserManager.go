package manager

import (
	"api-service/src/bo/request"
	"api-service/src/bo/response"
	"crypto/sha256"
	"fmt"
	"k/app"
	"k/db"
	"k/logger"
	"time"
)

type UserManager struct {
	A *app.App
}

// User 用户实体
type User struct {
	UserId     int64     `db:"user_id" json:"userId"`
	Username   string    `db:"username" json:"username"`
	Email      string    `db:"email" json:"email"`
	Password   string    `db:"password" json:"-"` // 不在JSON中显示
	FullName   string    `db:"full_name" json:"fullName"`
	Phone      string    `db:"phone" json:"phone"`
	Department string    `db:"department" json:"department"`
	Role       string    `db:"role" json:"role"`
	Status     string    `db:"status" json:"status"`
	CreateTime time.Time `db:"create_time" json:"createTime"`
	UpdateTime time.Time `db:"update_time" json:"updateTime"`
	LastLogin  time.Time `db:"last_login" json:"lastLogin"`
}

func (u *User) Table() string {
	return "api_user_t"
}

// CreateUser 创建用户
func (um *UserManager) CreateUser(req *request.CreateUserRequest) (*response.CreateUserResponse, error) {
	resp := response.NewCreateUserResponse()
	
	// 检查用户名是否已存在
	if um.IsUsernameExists(req.Username) {
		resp.Header.FunctionExecutionStatus.Status = "FAIL"
		resp.Header.FunctionExecutionStatus.StatusCodeData = &response.StatusCode{
			SubjectCode: "USERNAME",
			ReasonCode:  "ALREADY_EXISTS",
			Message:     "用户名已存在",
		}
		return resp, nil
	}
	
	// 检查邮箱是否已存在
	if um.IsEmailExists(req.Email) {
		resp.Header.FunctionExecutionStatus.Status = "FAIL"
		resp.Header.FunctionExecutionStatus.StatusCodeData = &response.StatusCode{
			SubjectCode: "EMAIL",
			ReasonCode:  "ALREADY_EXISTS",
			Message:     "邮箱已存在",
		}
		return resp, nil
	}
	
	// 创建用户实体
	user := &User{
		Username:   req.Username,
		Email:      req.Email,
		Password:   um.HashPassword(req.Password),
		FullName:   req.FullName,
		Phone:      req.Phone,
		Department: req.Department,
		Role:       req.Role,
		Status:     "active",
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
	}
	
	// 插入数据库
	da := um.A.DA()
	da.Begin()
	defer da.Rollback()
	
	userId, err := da.InsertEntity(user, um.A.Instance.Id)
	if err != nil {
		logger.E("CreateUser", err)
		resp.Header.FunctionExecutionStatus.Status = "FAIL"
		resp.Header.FunctionExecutionStatus.StatusCodeData = &response.StatusCode{
			SubjectCode: "DATABASE",
			ReasonCode:  "INSERT_FAILED",
			Message:     "创建用户失败",
		}
		return resp, err
	}
	
	da.Commit()
	
	resp.UserId = int64(userId)
	resp.Username = req.Username
	resp.Message = "用户创建成功"
	
	return resp, nil
}

// GetUser 获取用户信息
func (um *UserManager) GetUser(userId int64) (*response.UserInfo, error) {
	user, err := db.SelectEntity[*User](um.A.DA(), "user_id=?", userId)
	if err != nil {
		return nil, err
	}
	
	if user == nil {
		return nil, fmt.Errorf("用户不存在")
	}
	
	return um.convertToUserInfo(user), nil
}

// ListUsers 获取用户列表
func (um *UserManager) ListUsers(req *request.ListUsersRequest) (*response.ListUsersResponse, error) {
	resp := response.NewListUsersResponse()
	
	// 构建查询条件
	whereClause := "1=1"
	args := []interface{}{}
	
	if req.Username != "" {
		whereClause += " AND username LIKE ?"
		args = append(args, "%"+req.Username+"%")
	}
	
	if req.Email != "" {
		whereClause += " AND email LIKE ?"
		args = append(args, "%"+req.Email+"%")
	}
	
	if req.Department != "" {
		whereClause += " AND department = ?"
		args = append(args, req.Department)
	}
	
	if req.Role != "" {
		whereClause += " AND role = ?"
		args = append(args, req.Role)
	}
	
	if req.Status != "" {
		whereClause += " AND status = ?"
		args = append(args, req.Status)
	}
	
	// 查询总数
	countSql := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s", (&User{}).Table(), whereClause)
	total, err := um.A.DA().SelectInt(countSql, args...)
	if err != nil {
		return resp, err
	}
	
	// 查询数据
	offset := (req.PageNum - 1) * req.PageSize
	dataSql := fmt.Sprintf("SELECT * FROM %s WHERE %s ORDER BY create_time DESC LIMIT %d OFFSET %d", 
		(&User{}).Table(), whereClause, req.PageSize, offset)
	
	users, err := db.SelectList[*User](um.A.DA(), dataSql, args...)
	if err != nil {
		return resp, err
	}
	
	// 转换为响应格式
	userInfos := make([]*response.UserInfo, len(users))
	for i, user := range users {
		userInfos[i] = um.convertToUserInfo(user)
	}
	
	resp.Users = userInfos
	resp.Total = total
	resp.PageNum = req.PageNum
	resp.PageSize = req.PageSize
	resp.TotalPages = int32((total + int64(req.PageSize) - 1) / int64(req.PageSize))
	
	return resp, nil
}

// UpdateUser 更新用户
func (um *UserManager) UpdateUser(req *request.UpdateUserRequest) error {
	da := um.A.DA()
	da.Begin()
	defer da.Rollback()
	
	// 构建更新字段
	updateFields := []string{}
	args := []interface{}{}
	
	if req.Email != "" {
		updateFields = append(updateFields, "email=?")
		args = append(args, req.Email)
	}
	
	if req.FullName != "" {
		updateFields = append(updateFields, "full_name=?")
		args = append(args, req.FullName)
	}
	
	if req.Phone != "" {
		updateFields = append(updateFields, "phone=?")
		args = append(args, req.Phone)
	}
	
	if req.Department != "" {
		updateFields = append(updateFields, "department=?")
		args = append(args, req.Department)
	}
	
	if req.Role != "" {
		updateFields = append(updateFields, "role=?")
		args = append(args, req.Role)
	}
	
	if req.Status != "" {
		updateFields = append(updateFields, "status=?")
		args = append(args, req.Status)
	}
	
	updateFields = append(updateFields, "update_time=?")
	args = append(args, time.Now())
	args = append(args, req.UserId)
	
	updateSql := fmt.Sprintf("UPDATE %s SET %s WHERE user_id=?", 
		(&User{}).Table(), fmt.Sprintf("%s", updateFields))
	
	_, err := da.Exec(updateSql, args...)
	if err != nil {
		return err
	}
	
	da.Commit()
	return nil
}

// DeleteUser 删除用户
func (um *UserManager) DeleteUser(userId int64) error {
	da := um.A.DA()
	da.Begin()
	defer da.Rollback()
	
	_, err := da.Delete((&User{}).Table(), "user_id=?", userId)
	if err != nil {
		return err
	}
	
	da.Commit()
	return nil
}

// IsUsernameExists 检查用户名是否存在
func (um *UserManager) IsUsernameExists(username string) bool {
	count, err := um.A.DA().SelectInt("SELECT COUNT(*) FROM "+(&User{}).Table()+" WHERE username=?", username)
	if err != nil {
		logger.E("IsUsernameExists", err)
		return false
	}
	return count > 0
}

// IsEmailExists 检查邮箱是否存在
func (um *UserManager) IsEmailExists(email string) bool {
	count, err := um.A.DA().SelectInt("SELECT COUNT(*) FROM "+(&User{}).Table()+" WHERE email=?", email)
	if err != nil {
		logger.E("IsEmailExists", err)
		return false
	}
	return count > 0
}

// HashPassword 密码哈希
func (um *UserManager) HashPassword(password string) string {
	hash := sha256.Sum256([]byte(password))
	return fmt.Sprintf("%x", hash)
}

// convertToUserInfo 转换为用户信息响应
func (um *UserManager) convertToUserInfo(user *User) *response.UserInfo {
	return &response.UserInfo{
		UserId:     user.UserId,
		Username:   user.Username,
		Email:      user.Email,
		FullName:   user.FullName,
		Phone:      user.Phone,
		Department: user.Department,
		Role:       user.Role,
		Status:     user.Status,
		CreateTime: user.CreateTime,
		UpdateTime: user.UpdateTime,
		LastLogin:  user.LastLogin,
	}
}
