package manager

import (
	"api-service/src/bo/request"
	"fmt"
	"k/app"
	"k/logger"
	"strings"
	"time"
)

type DataManager struct {
	A *app.App
}

// QueryData 查询数据
func (dm *DataManager) QueryData(req *request.QueryDataRequest) ([]map[string]interface{}, int64, error) {
	// 构建查询条件
	whereClause := "1=1"
	args := []interface{}{}
	
	if req.Conditions != nil {
		for field, value := range req.Conditions {
			whereClause += fmt.Sprintf(" AND %s=?", field)
			args = append(args, value)
		}
	}
	
	// 查询总数
	countSql := fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE %s", req.TableName, whereClause)
	total, err := dm.A.DA().SelectInt(countSql, args...)
	if err != nil {
		logger.E("QueryData Count", err)
		return nil, 0, err
	}
	
	// 构建查询SQL
	orderBy := "id DESC"
	if req.OrderBy != "" {
		orderBy = req.OrderBy
	}
	
	offset := (req.PageNum - 1) * req.PageSize
	dataSql := fmt.Sprintf("SELECT * FROM %s WHERE %s ORDER BY %s LIMIT %d OFFSET %d",
		req.TableName, whereClause, orderBy, req.PageSize, offset)
	
	// 执行查询
	rows, err := dm.A.DA().Select(dataSql, args...)
	if err != nil {
		logger.E("QueryData", err)
		return nil, 0, err
	}
	
	return rows, total, nil
}

// CreateData 创建数据
func (dm *DataManager) CreateData(req *request.CreateDataRequest) (int64, error) {
	da := dm.A.DA()
	da.Begin()
	defer da.Rollback()
	
	// 构建插入SQL
	fields := []string{}
	placeholders := []string{}
	values := []interface{}{}
	
	// 添加创建时间
	req.Data["create_time"] = time.Now()
	req.Data["update_time"] = time.Now()
	
	for field, value := range req.Data {
		fields = append(fields, field)
		placeholders = append(placeholders, "?")
		values = append(values, value)
	}
	
	insertSql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		req.TableName,
		strings.Join(fields, ","),
		strings.Join(placeholders, ","))
	
	result, err := da.Exec(insertSql, values...)
	if err != nil {
		logger.E("CreateData", err)
		return 0, err
	}
	
	id, err := result.LastInsertId()
	if err != nil {
		logger.E("CreateData LastInsertId", err)
		return 0, err
	}
	
	da.Commit()
	return id, nil
}

// UpdateData 更新数据
func (dm *DataManager) UpdateData(req *request.UpdateDataRequest) error {
	da := dm.A.DA()
	da.Begin()
	defer da.Rollback()
	
	// 构建更新SQL
	setFields := []string{}
	values := []interface{}{}
	
	// 添加更新时间
	req.Data["update_time"] = time.Now()
	
	for field, value := range req.Data {
		setFields = append(setFields, fmt.Sprintf("%s=?", field))
		values = append(values, value)
	}
	
	values = append(values, req.Id)
	
	updateSql := fmt.Sprintf("UPDATE %s SET %s WHERE id=?",
		req.TableName,
		strings.Join(setFields, ","))
	
	_, err := da.Exec(updateSql, values...)
	if err != nil {
		logger.E("UpdateData", err)
		return err
	}
	
	da.Commit()
	return nil
}

// DeleteData 删除数据
func (dm *DataManager) DeleteData(req *request.DeleteDataRequest) error {
	da := dm.A.DA()
	da.Begin()
	defer da.Rollback()
	
	_, err := da.Delete(req.TableName, "id=?", req.Id)
	if err != nil {
		logger.E("DeleteData", err)
		return err
	}
	
	da.Commit()
	return nil
}

// BatchOperation 批量操作
func (dm *DataManager) BatchOperation(req *request.BatchDataRequest) ([]int64, error) {
	da := dm.A.DA()
	da.Begin()
	defer da.Rollback()
	
	results := []int64{}
	
	switch req.Operation {
	case "create":
		for _, item := range req.Items {
			createReq := &request.CreateDataRequest{
				TableName: req.TableName,
				Data:      item,
			}
			id, err := dm.CreateData(createReq)
			if err != nil {
				logger.E("BatchOperation Create", err)
				return nil, err
			}
			results = append(results, id)
		}
		
	case "update":
		for _, item := range req.Items {
			if id, ok := item["id"].(int64); ok {
				delete(item, "id") // 移除id字段，避免在SET子句中出现
				updateReq := &request.UpdateDataRequest{
					TableName: req.TableName,
					Id:        id,
					Data:      item,
				}
				err := dm.UpdateData(updateReq)
				if err != nil {
					logger.E("BatchOperation Update", err)
					return nil, err
				}
				results = append(results, id)
			}
		}
		
	case "delete":
		for _, item := range req.Items {
			if id, ok := item["id"].(int64); ok {
				deleteReq := &request.DeleteDataRequest{
					TableName: req.TableName,
					Id:        id,
				}
				err := dm.DeleteData(deleteReq)
				if err != nil {
					logger.E("BatchOperation Delete", err)
					return nil, err
				}
				results = append(results, id)
			}
		}
	}
	
	da.Commit()
	return results, nil
}

// GetStatistics 获取数据统计
func (dm *DataManager) GetStatistics(req *request.DataStatisticsRequest) ([]map[string]interface{}, error) {
	// 构建查询条件
	whereClause := "1=1"
	args := []interface{}{}
	
	if req.Conditions != nil {
		for field, value := range req.Conditions {
			whereClause += fmt.Sprintf(" AND %s=?", field)
			args = append(args, value)
		}
	}
	
	// 添加日期范围条件
	if req.DateRange != nil {
		whereClause += " AND create_time BETWEEN ? AND ?"
		args = append(args, req.DateRange.StartDate, req.DateRange.EndDate)
	}
	
	// 构建聚合字段
	selectFields := []string{}
	if len(req.GroupBy) > 0 {
		selectFields = append(selectFields, strings.Join(req.GroupBy, ","))
	}
	
	if req.Aggregates != nil {
		for field, function := range req.Aggregates {
			selectFields = append(selectFields, fmt.Sprintf("%s(%s) as %s_%s", 
				strings.ToUpper(function), field, field, function))
		}
	}
	
	// 构建GROUP BY子句
	groupByClause := ""
	if len(req.GroupBy) > 0 {
		groupByClause = " GROUP BY " + strings.Join(req.GroupBy, ",")
	}
	
	// 构建统计SQL
	statsSql := fmt.Sprintf("SELECT %s FROM %s WHERE %s%s",
		strings.Join(selectFields, ","),
		req.TableName,
		whereClause,
		groupByClause)
	
	// 执行查询
	rows, err := dm.A.DA().Select(statsSql, args...)
	if err != nil {
		logger.E("GetStatistics", err)
		return nil, err
	}
	
	return rows, nil
}

// ValidateTableName 验证表名（防止SQL注入）
func (dm *DataManager) ValidateTableName(tableName string) bool {
	// 简单的表名验证，只允许字母、数字和下划线
	for _, char := range tableName {
		if !((char >= 'a' && char <= 'z') || 
			 (char >= 'A' && char <= 'Z') || 
			 (char >= '0' && char <= '9') || 
			 char == '_') {
			return false
		}
	}
	return len(tableName) > 0 && len(tableName) <= 64
}
