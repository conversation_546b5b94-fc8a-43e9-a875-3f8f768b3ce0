package handler

import (
	"api-service/src/bl/manager"
	"api-service/src/bo/request"
	"esim.common/src/bo"
	"fmt"
	"k"
	"k/app"
	"k/logger"
)

type DataHandler struct {
	A  *app.App
	dm *manager.DataManager
	cm *manager.CacheManager
}

func (dh *DataHandler) Init() {
	dh.dm = app.GetManager[*manager.DataManager](dh.A)
	dh.cm = app.GetManager[*manager.CacheManager](dh.A)
}

// QueryData 查询数据
func (dh *DataHandler) QueryData(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "QueryDataException")
		}
	}()
	
	// 解析请求
	queryRequest := k.FromJson[*request.QueryDataRequest](string(request.GetBody()))
	queryRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(queryRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 设置默认分页参数
	if queryRequest.PageNum <= 0 {
		queryRequest.PageNum = 1
	}
	if queryRequest.PageSize <= 0 {
		queryRequest.PageSize = 20
	}
	
	// 查询数据
	data, total, err := dh.dm.QueryData(queryRequest)
	if err != nil {
		logger.E("QueryData", err)
		bo.NewInternalExceptionResponse(response, "查询数据失败")
		return
	}
	
	// 构建响应
	queryResponse := map[string]interface{}{
		"data":       data,
		"total":      total,
		"pageNum":    queryRequest.PageNum,
		"pageSize":   queryRequest.PageSize,
		"totalPages": (total + int64(queryRequest.PageSize) - 1) / int64(queryRequest.PageSize),
	}
	
	response.WriteJson(queryResponse)
}

// CreateData 创建数据
func (dh *DataHandler) CreateData(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "CreateDataException")
		}
	}()
	
	// 解析请求
	createRequest := k.FromJson[*request.CreateDataRequest](string(request.GetBody()))
	createRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(createRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 创建数据
	id, err := dh.dm.CreateData(createRequest)
	if err != nil {
		logger.E("CreateData", err)
		bo.NewInternalExceptionResponse(response, "创建数据失败")
		return
	}
	
	// 清除相关缓存
	dh.cm.DeletePattern(fmt.Sprintf("data:%s:*", createRequest.TableName))
	
	// 构建响应
	createResponse := map[string]interface{}{
		"success": true,
		"id":      id,
		"message": "数据创建成功",
	}
	
	response.WriteJson(createResponse)
}

// UpdateData 更新数据
func (dh *DataHandler) UpdateData(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateDataException")
		}
	}()
	
	// 解析请求
	updateRequest := k.FromJson[*request.UpdateDataRequest](string(request.GetBody()))
	updateRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(updateRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 更新数据
	err := dh.dm.UpdateData(updateRequest)
	if err != nil {
		logger.E("UpdateData", err)
		bo.NewInternalExceptionResponse(response, "更新数据失败")
		return
	}
	
	// 清除相关缓存
	dh.cm.DeletePattern(fmt.Sprintf("data:%s:*", updateRequest.TableName))
	
	// 构建响应
	updateResponse := map[string]interface{}{
		"success": true,
		"message": "数据更新成功",
	}
	
	response.WriteJson(updateResponse)
}

// DeleteData 删除数据
func (dh *DataHandler) DeleteData(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "DeleteDataException")
		}
	}()
	
	// 解析请求
	deleteRequest := k.FromJson[*request.DeleteDataRequest](string(request.GetBody()))
	deleteRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(deleteRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 删除数据
	err := dh.dm.DeleteData(deleteRequest)
	if err != nil {
		logger.E("DeleteData", err)
		bo.NewInternalExceptionResponse(response, "删除数据失败")
		return
	}
	
	// 清除相关缓存
	dh.cm.DeletePattern(fmt.Sprintf("data:%s:*", deleteRequest.TableName))
	
	// 构建响应
	deleteResponse := map[string]interface{}{
		"success": true,
		"message": "数据删除成功",
	}
	
	response.WriteJson(deleteResponse)
}

// BatchOperation 批量操作
func (dh *DataHandler) BatchOperation(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "BatchOperationException")
		}
	}()
	
	// 解析请求
	batchRequest := k.FromJson[*request.BatchDataRequest](string(request.GetBody()))
	batchRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(batchRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 批量操作
	results, err := dh.dm.BatchOperation(batchRequest)
	if err != nil {
		logger.E("BatchOperation", err)
		bo.NewInternalExceptionResponse(response, "批量操作失败")
		return
	}
	
	// 清除相关缓存
	dh.cm.DeletePattern(fmt.Sprintf("data:%s:*", batchRequest.TableName))
	
	// 构建响应
	batchResponse := map[string]interface{}{
		"success": true,
		"results": results,
		"count":   len(results),
		"message": fmt.Sprintf("批量%s操作成功", batchRequest.Operation),
	}
	
	response.WriteJson(batchResponse)
}

// GetStatistics 获取数据统计
func (dh *DataHandler) GetStatistics(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetStatisticsException")
		}
	}()
	
	// 解析请求
	statsRequest := k.FromJson[*request.DataStatisticsRequest](string(request.GetBody()))
	statsRequest.Header.Check(response)
	
	// 验证表名
	if !dh.dm.ValidateTableName(statsRequest.TableName) {
		bo.NewBadRequestResponse(response, "无效的表名")
		return
	}
	
	// 获取统计数据
	stats, err := dh.dm.GetStatistics(statsRequest)
	if err != nil {
		logger.E("GetStatistics", err)
		bo.NewInternalExceptionResponse(response, "获取统计数据失败")
		return
	}
	
	// 构建响应
	statsResponse := map[string]interface{}{
		"statistics": stats,
		"count":      len(stats),
		"message":    "统计数据获取成功",
	}
	
	response.WriteJson(statsResponse)
}
