package handler

import (
	"api-service/src"
	"api-service/src/bl/manager"
	"esim.common/src/bo"
	"k"
	"k/app"
	"k/logger"
	"runtime"
	"time"
)

type SystemHandler struct {
	A     *app.App
	cm    *manager.CacheManager
	param *src.ApiParam
}

func (sh *SystemHandler) Init() {
	sh.cm = app.GetManager[*manager.CacheManager](sh.A)
	sh.param = app.GetClientParam[*src.ApiParam](sh.A)
}

// GetSystemInfo 获取系统信息
func (sh *SystemHandler) GetSystemInfo(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetSystemInfoException")
		}
	}()
	
	// 获取系统信息
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	systemInfo := map[string]interface{}{
		"application": map[string]interface{}{
			"name":      "API Service",
			"version":   "1.0.0",
			"buildTime": "2025-08-07",
			"goVersion": runtime.Version(),
		},
		"runtime": map[string]interface{}{
			"goroutines":   runtime.NumGoroutine(),
			"cpus":         runtime.NumCPU(),
			"memoryAlloc":  memStats.Alloc,
			"memoryTotal":  memStats.TotalAlloc,
			"memorySys":    memStats.Sys,
			"gcRuns":       memStats.NumGC,
		},
		"server": map[string]interface{}{
			"instanceId": sh.A.Instance.Id,
			"startTime":  sh.A.Instance.StartTime,
			"uptime":     time.Since(sh.A.Instance.StartTime).Seconds(),
		},
		"cache": sh.cm.GetStats(),
	}
	
	response.WriteJson(systemInfo)
}

// GetSystemConfig 获取系统配置
func (sh *SystemHandler) GetSystemConfig(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetSystemConfigException")
		}
	}()
	
	// 获取系统配置（脱敏处理）
	config := map[string]interface{}{
		"enableKms":             sh.param.EnableKms,
		"cacheExpireTime":       sh.param.CacheExpireTime,
		"maxConcurrentRequests": sh.param.MaxConcurrentRequests,
		"rateLimitPerSecond":    sh.param.RateLimitPerSecond,
		"enableDataValidation":  sh.param.EnableDataValidation,
		"defaultPageSize":       sh.param.DefaultPageSize,
		"maxPageSize":           sh.param.MaxPageSize,
		"externalApiTimeout":    sh.param.ExternalApiTimeout,
		"logLevel":              sh.param.LogLevel,
		"enableAccessLog":       sh.param.EnableAccessLog,
		// 敏感信息不返回
		"kmsApiUrl":    maskSensitiveInfo(sh.param.KmsApiUrl),
		"externalApiUrl": maskSensitiveInfo(sh.param.ExternalApiUrl),
	}
	
	response.WriteJson(config)
}

// UpdateSystemConfig 更新系统配置
func (sh *SystemHandler) UpdateSystemConfig(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "UpdateSystemConfigException")
		}
	}()
	
	// 解析请求
	configRequest := k.FromJson[map[string]interface{}](string(request.GetBody()))
	
	// 这里应该实现配置更新逻辑
	// 由于配置通常需要重启应用才能生效，这里只是示例
	
	updateResponse := map[string]interface{}{
		"success": true,
		"message": "配置更新成功，部分配置需要重启应用后生效",
	}
	
	response.WriteJson(updateResponse)
}

// ManageCache 缓存管理
func (sh *SystemHandler) ManageCache(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "ManageCacheException")
		}
	}()
	
	// 解析请求
	cacheRequest := k.FromJson[map[string]interface{}](string(request.GetBody()))
	
	action, ok := cacheRequest["action"].(string)
	if !ok {
		bo.NewBadRequestResponse(response, "缺少action参数")
		return
	}
	
	var result map[string]interface{}
	
	switch action {
	case "clear":
		pattern, _ := cacheRequest["pattern"].(string)
		if pattern == "" {
			sh.cm.Clear()
			result = map[string]interface{}{
				"success": true,
				"message": "所有缓存已清空",
			}
		} else {
			count := sh.cm.DeletePattern(pattern)
			result = map[string]interface{}{
				"success": true,
				"message": "匹配的缓存已清空",
				"count":   count,
			}
		}
		
	case "stats":
		result = map[string]interface{}{
			"success": true,
			"stats":   sh.cm.GetStats(),
		}
		
	case "keys":
		keys := sh.cm.Keys()
		result = map[string]interface{}{
			"success": true,
			"keys":    keys,
			"count":   len(keys),
		}
		
	case "get":
		key, _ := cacheRequest["key"].(string)
		if key == "" {
			bo.NewBadRequestResponse(response, "缺少key参数")
			return
		}
		value, exists := sh.cm.Get(key)
		result = map[string]interface{}{
			"success": true,
			"exists":  exists,
			"value":   value,
		}
		
	case "delete":
		key, _ := cacheRequest["key"].(string)
		if key == "" {
			bo.NewBadRequestResponse(response, "缺少key参数")
			return
		}
		sh.cm.Delete(key)
		result = map[string]interface{}{
			"success": true,
			"message": "缓存已删除",
		}
		
	default:
		bo.NewBadRequestResponse(response, "不支持的操作: "+action)
		return
	}
	
	response.WriteJson(result)
}

// GetSystemStats 获取系统统计
func (sh *SystemHandler) GetSystemStats(request *app.Request, response *app.Response) {
	defer func() {
		if err := recover(); err != nil {
			logger.E(request.Path, err)
			bo.NewInternalExceptionResponse(response, "GetSystemStatsException")
		}
	}()
	
	// 获取系统统计信息
	stats := map[string]interface{}{
		"cache":   sh.cm.GetStats(),
		"runtime": getRuntimeStats(),
		"memory":  getMemoryStats(),
	}
	
	response.WriteJson(stats)
}

// maskSensitiveInfo 脱敏敏感信息
func maskSensitiveInfo(info string) string {
	if len(info) <= 8 {
		return "****"
	}
	return info[:4] + "****" + info[len(info)-4:]
}

// getRuntimeStats 获取运行时统计
func getRuntimeStats() map[string]interface{} {
	return map[string]interface{}{
		"goroutines": runtime.NumGoroutine(),
		"cpus":       runtime.NumCPU(),
		"goVersion":  runtime.Version(),
	}
}

// getMemoryStats 获取内存统计
func getMemoryStats() map[string]interface{} {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	
	return map[string]interface{}{
		"alloc":      memStats.Alloc,
		"totalAlloc": memStats.TotalAlloc,
		"sys":        memStats.Sys,
		"numGC":      memStats.NumGC,
		"heapAlloc":  memStats.HeapAlloc,
		"heapSys":    memStats.HeapSys,
		"heapIdle":   memStats.HeapIdle,
		"heapInuse":  memStats.HeapInuse,
	}
}
