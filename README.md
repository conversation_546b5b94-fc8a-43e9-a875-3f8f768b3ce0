# API Service

基于ES2+框架开发的通用API服务，提供用户管理、数据管理、系统监控等功能。

## 项目结构

```
api-service/
├── main.go                 # 应用入口
├── go.mod                 # Go模块定义
├── app.properties         # 应用配置
├── src/
│   ├── param.go          # 参数配置
│   ├── version.go        # 版本管理
│   ├── bl/               # 业务逻辑层
│   │   ├── handler/      # 业务处理器
│   │   └── manager/      # 业务管理器
│   ├── bo/               # 业务对象
│   │   ├── request/      # 请求对象
│   │   └── response/     # 响应对象
│   └── ui/               # UI事件层
├── docs/                 # 文档
└── tests/               # 测试文件
```

## 功能特性

### 🔐 用户管理
- 用户创建、查询、更新、删除
- 密码管理
- 角色权限控制
- 用户列表分页查询

### 📊 数据管理
- 通用数据CRUD操作
- 批量数据操作
- 数据统计分析
- 条件查询和排序

### 🖥️ 系统管理
- 系统信息查询
- 配置管理
- 缓存管理
- 系统统计

### 🏥 健康监控
- 健康检查
- 服务状态监控
- 性能指标

## API接口

### 用户管理接口

#### 创建用户
```
POST /api/user/create
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CREATE_USER_001"
  },
  "username": "testuser",
  "email": "<EMAIL>",
  "password": "password123",
  "fullName": "Test User",
  "phone": "13800138000",
  "department": "IT",
  "role": "user"
}
```

#### 获取用户信息
```
POST /api/user/get
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "GET_USER_001"
  },
  "userId": 1
}
```

#### 用户列表
```
POST /api/user/list
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "LIST_USERS_001"
  },
  "pageNum": 1,
  "pageSize": 20,
  "username": "",
  "role": "user",
  "status": "active"
}
```

### 数据管理接口

#### 查询数据
```
POST /api/data/query
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "QUERY_DATA_001"
  },
  "tableName": "user_data",
  "conditions": {
    "status": "active",
    "department": "IT"
  },
  "orderBy": "create_time DESC",
  "pageNum": 1,
  "pageSize": 20
}
```

#### 创建数据
```
POST /api/data/create
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CREATE_DATA_001"
  },
  "tableName": "user_data",
  "data": {
    "name": "Test Data",
    "value": "123",
    "status": "active"
  }
}
```

### 系统管理接口

#### 系统信息
```
POST /api/system/info
```

#### 缓存管理
```
POST /api/system/cache
```

请求体：
```json
{
  "header": {
    "functionRequesterIdentifier": "API_CLIENT",
    "functionCallIdentifier": "CACHE_MANAGE_001"
  },
  "action": "clear",
  "pattern": "users:*"
}
```

### 健康检查接口

#### 健康检查
```
GET /api/health/check
```

#### 服务状态
```
GET /api/health/status
```

## 配置说明

### app.properties 配置项

```properties
# 基础配置
InstanceId=50
GID=API_SERVICE
ListenPort=9004

# KMS配置（可选）
EnableKms=false
KmsApiKey=
KmsApiSecret=
KmsApiUrl=

# 性能配置
CacheExpireTime=3600
MaxConcurrentRequests=1000
RateLimitPerSecond=100
```

## 部署说明

### 1. 环境要求
- Go 1.22+
- MySQL 5.7+
- 依赖k框架和esim.common

### 2. 编译运行
```bash
# 编译
go build -o api-service main.go

# 运行
./api-service
```

### 3. 数据库初始化
```sql
-- 创建用户表
CREATE TABLE api_user_t (
    user_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    department VARCHAR(100),
    role VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    last_login TIMESTAMP NULL
);

-- 创建索引
CREATE INDEX idx_user_username ON api_user_t(username);
CREATE INDEX idx_user_email ON api_user_t(email);
CREATE INDEX idx_user_role ON api_user_t(role);
CREATE INDEX idx_user_status ON api_user_t(status);
```

## 开发指南

### 添加新的API接口

1. **定义请求/响应对象**
   - 在 `src/bo/request/` 中定义请求对象
   - 在 `src/bo/response/` 中定义响应对象

2. **创建Manager**
   - 在 `src/bl/manager/` 中实现业务逻辑

3. **创建Handler**
   - 在 `src/bl/handler/` 中实现请求处理

4. **创建Event**
   - 在 `src/ui/` 中定义事件处理器

5. **注册路由**
   - 在 `main.go` 中注册新的事件路由

### 最佳实践

1. **错误处理**
   - 使用统一的错误响应格式
   - 记录详细的错误日志

2. **缓存策略**
   - 合理使用缓存提高性能
   - 及时清理过期缓存

3. **数据验证**
   - 在Handler层进行参数验证
   - 使用validate标签进行自动验证

4. **事务管理**
   - 在Manager层处理数据库事务
   - 确保数据一致性

## 版本历史

- v1.0.0 (2025-08-07): 初始版本，支持用户管理、数据管理、系统监控

## 许可证

内部项目，仅供公司内部使用。
